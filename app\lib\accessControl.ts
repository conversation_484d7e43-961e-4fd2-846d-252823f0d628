"use server";

import { IMenuItem } from "@/interface/IMenuItem";

// Definerer strukturen for interne menyelementer, inkludert rollebasert tilgangskontroll
interface MenuItemInternal {
  label: string;
  path?: string;
  roles?: string[];
  children?: MenuItemInternal[];
}

// Array av menyelementer med rollebasert tilgangskontroll
const menuItems: MenuItemInternal[] = [
  {
    label: "Start",
    path: "/",
  },
  {
    label: "PGS-monitor",
    path: "/pgs-monitor",
  },
  {
    label: "Gruppeopplasting",
    path: "/gruppeopplaster",
  },
  {
    label: "Nedlasting",
    path: "/nedlasting",
    roles: [
      "urn:udir:pgsa:administrator",
      "urn:udir:eksamen:sa",
      "urn:udir:eksamen:sa+",
      "urn:udir:eksamen:fk",
      "urn:udir:eksamen:sf",
      "urn:udir:eksamen:ko",
    ],
  },

  {
    label: "Hjelp",
    children: [
      { label: "Om PGS", path: "/omPGS" },
      { label: "Brukerveiledning", path: "/brukerveiledning" },
      { label: "Fagkoder vgs i PGS", path: "/fagkoderVGS" },
      { label: "Tilbakemelding", path: "/tilbakemelding" },
    ],
  },

  {
    label: "Admin",
    children: [
      {
        label: "Importer eksamensplan",
        path: "/admin/importer-eksamensplan",
        roles: ["urn:udir:pgsa:administrator"],
      },
      {
        label: "Kandidatstatus",
        path: "/kandidatstatus",
        roles: ["urn:udir:pgsa:administrator"],
      },
      {
        label: "Kandidatsøk",
        path: "/kandidatsok",
        roles: ["urn:udir:pgsa:administrator"],
      },
      /*  {
        label: "Meldinger",
        path: "/message",
        roles: ["urn:udir:pgsa:administrator"],
      }, */
    ],
  },
];

/**
 * Sjekker om en bruker har riktig rolle for å få tilgang til et menyelement.
 * @param userRoles - Array med roller tildelt brukeren.
 * @param itemRoles - Array med roller som kreves for menyelementet (valgfritt).
 * @returns Sann hvis brukeren har minst én av de påkrevde rollene, eller hvis ingen roller er spesifisert.
 */
const hasCorrectRole = (userRoles: string[], itemRoles?: string[]): boolean => {
  if (!itemRoles || itemRoles.length === 0) return true;

  return userRoles.some((userRole) =>
    itemRoles.some((requiredRole) => {
      // Split both roles into their components
      const userRoleParts = userRole.split(":");
      const requiredRoleParts = requiredRole.split(":");

      // Check if all parts of the required role match the user role
      return requiredRoleParts.every(
        (part, index) =>
          userRoleParts[index] === part ||
          (index === requiredRoleParts.length - 1 &&
            userRoleParts[index]?.startsWith(part))
      );
    })
  );
};

/**
 * Filtrerer menyelementene basert på brukerens roller og returnerer tilgjengelige elementer.
 * @param userRoles - Array med roller tildelt brukeren.
 * @returns Array av tilgjengelige menyelementer med rolleinformasjon fjernet.
 */
export const getAccessibleMenuItems = async (
  userRoles: string | string[]
): Promise<IMenuItem[]> => {
  const roles = Array.isArray(userRoles) ? userRoles : [userRoles];

  const filterMenuItems = (items: MenuItemInternal[]): IMenuItem[] => {
    return items.reduce((acc: IMenuItem[], item) => {
      if (!hasCorrectRole(roles, item.roles)) {
        return acc;
      }

      const cleanItem: IMenuItem = {
        label: item.label,
        path: item.path,
      };

      if (item.children) {
        const filteredChildren = filterMenuItems(item.children);
        if (filteredChildren.length > 0) {
          cleanItem.children = filteredChildren;
        }
      }

      // Only add the item if it has a path or non-empty children
      if (
        cleanItem.path ||
        (cleanItem.children && cleanItem.children.length > 0)
      ) {
        acc.push(cleanItem);
      }

      return acc;
    }, []);
  };

  return filterMenuItems(menuItems);
};

/**
 * Sjekker om en bruker har tilgang til en bestemt sti basert på deres roller.
 * @param userRoles - Array med roller tildelt brukeren.
 * @param path - Stien som skal sjekkes for tilgang.
 * @returns Sann hvis brukeren har tilgang til stien, usann ellers.
 */
export const hasAccessToPath = async (
  userRoles: string | string[],
  path: string
): Promise<boolean> => {
  const roles = Array.isArray(userRoles) ? userRoles : [userRoles];

  const findMenuItem = (
    items: MenuItemInternal[],
    targetPath: string
  ): MenuItemInternal | undefined => {
    for (const item of items) {
      if (item.path === targetPath) {
        return item;
      }
      if (item.children) {
        const foundInChildren = findMenuItem(item.children, targetPath);
        if (foundInChildren) {
          return foundInChildren;
        }
      }
    }
    return undefined;
  };

  const menuItem = findMenuItem(menuItems, path);

  if (!menuItem) {
    // Hvis stien ikke finnes i menyen, anta at tilgang er tillatt
    return true;
  }

  return hasCorrectRole(roles, menuItem.roles);
};
