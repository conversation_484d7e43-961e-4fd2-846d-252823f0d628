import { Suspense, use } from "react";
import { getAllowedMimeTypes } from "@/lib/getAllowedMimeTypes";
import SubmitFiles from "./submitFiles";
import CandidateUploadSkeleton from "./candidateUploadSkeleton";
import BrukerVeiledningButton from "../../gruppeopplaster/brukerveiledningButton";
import GoBackButton from "./goBackButton";
import { Metadata } from "next";

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}
export const metadata: Metadata = {
  title: "Lever for - PGS-admin",
 
};

function Header() {
  return (
    <div className="bg-header">
      <div className="container-wrapper py-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
          <div>
            <h1 className="text-4xl">Lever for kandidat</h1>
          </div>
          <div className="mt-4 md:mt-0 ">
            <BrukerVeiledningButton />
          </div>
        </div>
      </div>
    </div>
  );
}

async function AsyncPageContent({ params }: PageProps) {
  let mimeTypes: IAllowedMimeTypes[] = [];
  let fetchError: string | null = null;

  try {
    mimeTypes = await getAllowedMimeTypes();
  } catch (err) {
    fetchError =
      "Det oppstod en feil ved lasting av tillatte filtyper. Vennligst prøv igjen senere.";
  }
  const resolvedParams = await params;

  return (
    <div className="flex flex-col gap-4 w-full">
      {fetchError ? (
        <div className="text-red-500 bg-red-100 p-4 rounded">{fetchError}</div>
      ) : (
        <SubmitFiles mimeTypes={mimeTypes} slug={resolvedParams.slug} />
      )}
    </div>
  );
}

export default function Page(props: PageProps) {
  const params = use(props.params);
  return (
    <div className="flex flex-col gap-6">
      <Header />
      <div className="container-wrapper-lg flex flex-col gap-4 mb-10">
        <GoBackButton />
        <Suspense fallback={<CandidateUploadSkeleton />}>
          <AsyncPageContent {...props} />
        </Suspense>
      </div>
    </div>
  );
}
