{
  "compilerOptions": {
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "moduleResolution": "node", // Changed from "bundler" to "node" for TypeORM compatibility
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "./*"
      ]
    },
    "target": "ES2017"
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "build/types/**/*.ts",
    "db/**/*.ts" // Explicitly include database files
  ],
  "exclude": [
    "node_modules"
  ],
  "ts-node": {
    // Added ts-node configuration for CLI operations
    "compilerOptions": {
      "module": "commonjs"
    }
  }
}
