"use client";

import { useState, useEffect } from "react";
import dayjs from "dayjs";
import { TestPartsEnum } from "@/enums/TestPart";
import { ExamStatusEnum } from "@/enums/ExamStatusEnum";
import { toast } from "@/components/ui/use-toast";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { apiUtils, blobUtils, generalUtils } from "./fileHandlerUtils";
import { useRole } from "@/context/RoleContext";
import { ActivityLogger, CandidateInfo } from "@/lib/services/ActivityLogger";
import { OperationEnum } from "@/enums/OperationEnum";

export interface FileHandlerConfig {
  componentName: string;
  endpoints: {
    validate: string;
    delete: string;
    updateStatus: string;
    download: string;
  };
  validateBeforeDelivery?: (file: IUploadedFile) => boolean;
  getUploadOperation: () => OperationEnum;
  getDeletionOperation: (file: IUploadedFile) => OperationEnum;
  getDeliveryOperation: (file: IUploadedFile) => OperationEnum;
  getCandidateInfo: (file?: IUploadedFile) => {
    userId: string;
    firstName: string;
    lastName: string;
    registrationId: string;
    candidateNumber: string;
  };
  isGroupUpload: boolean;
}

export interface BaseFileContextType {
  uploadedFiles: IUploadedFile[];
  isUploading: boolean;
  error: Error | null;
  addFile: (file: IUploadedFile) => Promise<void>;
  deleteFile: (fileGuid: string) => Promise<void>;
  clearFiles: () => void;
  setTestPart: (fileGuid: string, testPart: TestPartsEnum) => Promise<void>;
  updateFile: (fileGuid: string, updatedFields: Partial<IUploadedFile>) => void;
  deliverFile: (file: IUploadedFile) => Promise<void>;
  clearSubmittedFiles: () => void;
  downloadFile: (documentCode: string, filename: string) => Promise<void>;
  setUploadedFiles: React.Dispatch<React.SetStateAction<IUploadedFile[]>>;
}

export function useBaseFileHandler(
  config: FileHandlerConfig
): BaseFileContextType {
  const [uploadedFiles, setUploadedFiles] = useState<IUploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const { selectedRole } = useRole();

  useEffect(() => {
    setIsUploading(uploadedFiles.some((f) => !f.UploadFinished));
  }, [uploadedFiles]);

  const updateFile = (
    fileGuid: string,
    updatedFields: Partial<IUploadedFile>
  ) => {
    generalUtils.updateFileState(setUploadedFiles, fileGuid, updatedFields);
  };

  const addFile = async (newFile: IUploadedFile) => {
    const initialFile = {
      ...newFile,
      Name: newFile.Name.toUpperCase(),
      IsLoading: true,
      UploadFinished: false,
    };

    setUploadedFiles((prev) => [...prev, initialFile]);

    if (newFile.IsRejected) {
      updateFile(newFile.FileGuid, {
        IsRejected: true,
        UploadFinished: true,
        IsLoading: false,
      });
      return;
    }

    try {
      await blobUtils.upload(newFile);
      await apiUtils.validateUpload(
        newFile.FileGuid,
        newFile.TestPartId,
        config.endpoints.validate
      );

      const candidateInfo = config.getCandidateInfo(newFile);
      const candidateData: CandidateInfo = {
        userId: candidateInfo.userId,
        candidateName: `${candidateInfo.lastName}, ${candidateInfo.firstName}`,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.registrationId,
      };
      
      await ActivityLogger.logActivity(
        config.getUploadOperation(),
        candidateData,
        {
          roleName: `${selectedRole?.displayRoleName}`,
          fileName: newFile.Name,
          testPartId: newFile.TestPartId,
        }
      );

      updateFile(newFile.FileGuid, {
        UploadFinished: true,
        IsLoading: false,
      });
    } catch (error) {
      const err = await generalUtils.handleError(error, {
        component: config.componentName,
        action: "addFile",
        fileInfo: newFile,
      });

      updateFile(newFile.FileGuid, {
        IsRejected: true,
        UploadFinished: true,
        IsLoading: false,
        Errors: ["Opplasting feilet, prøv igjen!"],
      });

      setError(err);
    }
  };

  const deleteFile = async (fileGuid: string) => {
    updateFile(fileGuid, { IsDeleting: true });

    try {
      const fileToDelete = uploadedFiles.find((f) => f.FileGuid === fileGuid);
      if (!fileToDelete) throw new Error("File not found for deletion");

      if (!fileToDelete.IsRejected) {
        await apiUtils.deleteFileApi(fileGuid, config.endpoints.delete);

        const candidateInfo = config.getCandidateInfo(fileToDelete);
        const candidateData: CandidateInfo = {
          userId: candidateInfo.userId,
          candidateName: `${candidateInfo.lastName}, ${candidateInfo.firstName}`,
          candidateNumber: candidateInfo.candidateNumber,
          candidateRegistrationId: candidateInfo.registrationId,
        };
        
        await ActivityLogger.logActivity(
          config.getDeletionOperation(fileToDelete),
          candidateData,
          {
            roleName: `${selectedRole?.displayRoleName}`,
            fileName: fileToDelete.Name,
            testPartId: fileToDelete.TestPartId,
          }
        );
      }

      setUploadedFiles((prev) => prev.filter((f) => f.FileGuid !== fileGuid));
    } catch (error) {
      updateFile(fileGuid, { IsDeleting: false });
      await generalUtils.handleError(error, {
        component: config.componentName,
        action: "deleteFile",
        fileInfo: { FileGuid: fileGuid },
      });
      setError(error instanceof Error ? error : new Error(String(error)));
    }
  };

  const deliverFile = async (file: IUploadedFile) => {
    // Monitor-specific validation if provided
    if (config.validateBeforeDelivery && !config.validateBeforeDelivery(file)) {
      return; // Validation function should handle its own error messaging
    }

    updateFile(file.FileGuid, { IsSubmitting: true });

    try {
      const payload = generalUtils.createUpdatePayload(
        file,
        ExamStatusEnum.Submitted
      );
      await apiUtils.updateStatusApi(payload, config.endpoints.updateStatus);

      const candidateInfo = config.getCandidateInfo(file);
      const candidateData: CandidateInfo = {
        userId: candidateInfo.userId,
        candidateName: `${candidateInfo.lastName}, ${candidateInfo.firstName}`,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.registrationId,
      };

      // Handle special case for both parts
      if (file.TestPartId === TestPartsEnum.EksamenDel1ogDel2) {
        await ActivityLogger.logMonitorFileDeliveryBothParts(
          file,
          candidateData,
          `${selectedRole?.displayRoleName}`
        );
      } else {
        await ActivityLogger.logActivity(
          config.getDeliveryOperation(file),
          candidateData,
          {
            roleName: `${selectedRole?.displayRoleName}`,
            fileName: file.Name,
            testPartId: file.TestPartId,
          }
        );
      }

      updateFile(file.FileGuid, {
        Delivered: true,
        SubmittedDate: dayjs(),
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Feil ved levering av fil",
        description:
          "Det oppstod en feil ved levering av filen. Vennligst prøv igjen.",
      });

      await generalUtils.handleError(error, {
        component: config.componentName,
        action: "deliverFile",
        fileInfo: file,
      });
      setError(error instanceof Error ? error : new Error(String(error)));
    } finally {
      updateFile(file.FileGuid, { IsSubmitting: false });
    }
  };

  const setTestPart = async (fileGuid: string, testPart: TestPartsEnum) => {
    const file = uploadedFiles.find((f) => f.FileGuid === fileGuid);
    if (!file || file.TestPartId === 1) return;

    const oldTestPartId = file.TestPartId;
    updateFile(fileGuid, { IsLoading: true });

    try {
      const payload = generalUtils.createUpdatePayload(
        { ...file, TestPartId: testPart },
        ExamStatusEnum.Uploaded,
        oldTestPartId
      );
      await apiUtils.updateStatusApi(payload, config.endpoints.updateStatus);

      const candidateInfo = config.getCandidateInfo(file);
      const candidateData: CandidateInfo = {
        userId: candidateInfo.userId,
        candidateName: `${candidateInfo.lastName}, ${candidateInfo.firstName}`,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.registrationId,
      };
      
      await ActivityLogger.logTestPartChange(
        file,
        testPart,
        candidateData,
        `${selectedRole?.displayRoleName}`,
        config.isGroupUpload
      );

      updateFile(fileGuid, { TestPartId: testPart });
    } catch (error) {
      const err = await generalUtils.handleError(error, {
        component: config.componentName,
        action: "setTestPart",
        fileInfo: { ...file, TestPartId: testPart },
      });

      toast({
        variant: "destructive",
        title: "Feil ved oppsett av testdel",
        description:
          "Det oppstod en feil ved endring av eksamensdel. Vennligst prøv igjen.",
      });

      setError(err);
    } finally {
      updateFile(fileGuid, { IsLoading: false });
    }
  };

  const downloadFile = async (documentCode: string, filename: string) => {
    const fileToUpdate = uploadedFiles.find((f) => f.FileGuid === documentCode);
    const fileGuidForState = fileToUpdate
      ? fileToUpdate.FileGuid
      : documentCode;

    updateFile(fileGuidForState, { IsLoading: true });

    try {
      const blob = await apiUtils.downloadFileApi(
        documentCode,
        config.endpoints.download
      );
      blobUtils.download(blob, filename);

      const candidateInfo = config.getCandidateInfo(fileToUpdate);
      const candidateData: CandidateInfo = {
        userId: candidateInfo.userId,
        candidateName: `${candidateInfo.lastName}, ${candidateInfo.firstName}`,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.registrationId,
      };
      
      await ActivityLogger.logFileDownload(
        filename,
        fileToUpdate?.TestPartId || TestPartsEnum.Eksamen,
        candidateData,
        `${selectedRole?.displayRoleName}`
      );
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Nedlasting feilet for fil:",
        description: filename,
      });

      const err = await generalUtils.handleError(error, {
        component: config.componentName,
        action: "downloadFile",
        fileInfo: { FileGuid: documentCode, Name: filename },
      });
      setError(err);
    } finally {
      updateFile(fileGuidForState, { IsLoading: false });
    }
  };

  const clearFiles = () => setUploadedFiles([]);
  const clearSubmittedFiles = () =>
    setUploadedFiles((prev) => prev.filter((item) => !item.Delivered));

  return {
    uploadedFiles,
    isUploading,
    error,
    addFile,
    deleteFile,
    clearFiles,
    setTestPart,
    updateFile,
    deliverFile,
    clearSubmittedFiles,
    downloadFile,
    setUploadedFiles,
  };
}
