"use client";

import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import React, { useCallback, useEffect, useState } from "react";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { useCandidate } from "@/context/CandidateMonitorContext";
import { createColumns } from "./columns";
import { useDialogMonitor } from "@/context/DialogMonitorContext";
import ExamSubmissionActions from "@/components/examSubmissionActions";
import { useAccessRequests } from "@/hooks/accessRequestProvider";
import AccessStatusDisplay from "@/app/[locale]/pgs-monitor/accessStatusDisplay";
import { GrantAccessButton } from "@/components/grantAccessButton";
import { getCandidateAccessStatus } from "@/lib/candidateStatusUtils";
import {
  HiOutlineArrowNarrowDown,
  HiOutlineArrowNarrowUp,
} from "react-icons/hi";
import { useRole } from "@/context/RoleContext";
import { motion } from "framer-motion";

interface DataTableProps<TValue> {
  data: ICandidateMonitor[];
  resetPage?: boolean;
  examPaper: IExamPaperInternal;
  showIp: boolean;
}

export function DataTable<TValue>({
  data,
  resetPage,
  examPaper,
  showIp,
}: DataTableProps<TValue>) {
  const [sorting, setSorting] = useState<SortingState>([
    { id: "candidateName", desc: false }, // Sorter på navn stigende som default
  ]);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 50 });
  const [prevDataLength, setPrevDataLength] = useState(data.length);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  // --- SMOOTH ANIMATION ---
  const [sortKey, setSortKey] = useState(0);

  const {
    hasAbsence,
    authorizedCandidates,
    getTotalCandidateCount,
    updateVisibleCandidates,
    redisResult,
    removeBlockedUser,
    updateBlockedUsers,
    refreshRedisData,
    blockedUsers,
    candidatesWithLevertStatus,
    candidatesWaitingForExamStart,
    getLatestSession,
    setCurrentPageCandidates,
  } = useCandidate();
  const { selectedRole } = useRole();
  const { accessRequests } = useAccessRequests();
  const { openDialog } = useDialogMonitor();
  const isTwoPartExam = data.some(
    (candidate) => candidate.deliveryStatusPart2 !== -1
  );
  const isOnePartExam = data.some(
    (candidate) => candidate.deliveryStatusPart2 === -1
  );

  const columnProps = {
    showIp,
    onShowCandidateInfo: (candidate: any) => openDialog("info", candidate),
    onShowIpDialog: (candidate: any) => openDialog("ip", candidate),
    authorizedCandidates,
    accessRequests,
    blockedUsers,
    removeBlockedUser,
    updateBlockedUsers,
    redisResult,
    refreshRedisData,
    isTwoPartExam,
    isOnePartExam,
    candidatesWaitingForExamStart,
    getLatestSession,
  };

  // Create a sortable header for access column
  const createSortableHeader =
    (title: string) =>
    ({ column }: { column: any }) => {
      const isSorted = column.getIsSorted();
      const sortDirection = isSorted === "asc" ? "stigende" : "synkende";
      const ariaLabel = `Sorter etter ${title}, nåværende sortering: ${
        isSorted ? sortDirection : "ingen"
      }`;

      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
          aria-label={ariaLabel}
        >
          {title}
          <div
            className="ml-2 flex items-center -space-x-[6px]"
            aria-hidden="true"
          >
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    };

  // Function to get display status text for a candidate using centralized utility
  const getDisplayStatusText = (row: any) => {
    const statusResult = getCandidateAccessStatus({
      candidate: row.original,
      authorizedCandidates,
      accessRequests,
      blockedUsers,
      hasAbsence,
      candidatesWithLevertStatus,
      candidatesWaitingForExamStart,
    });

    return statusResult.text;
  };

  // Access column definition that was moved from columns.tsx
  const accessColumn = {
    accessorKey: "onlineStatus",
    header: createSortableHeader("Tilgang"),
    sortingFn: (rowA: any, rowB: any) => {
      const statusA = getDisplayStatusText(rowA);
      const statusB = getDisplayStatusText(rowB);
      return statusA.localeCompare(statusB);
    },
    cell: ({ row }: any) => {
      return (
        <div className="w-44 min-w-44 max-w-44 box-border overflow-hidden flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <AccessStatusDisplay
              status1={row.original.deliveryStatusPart1}
              status2={row.original.deliveryStatusPart2}
              candidate={row.original}
            />
          </div>
          <GrantAccessButton
            candidate={row.original}
            selectedRole={selectedRole}
          />
        </div>
      );
    },
    enableSorting: true,
    enableHiding: false,
  };

  // Get the regular columns from createColumns
  const baseColumns = createColumns(columnProps);

  // Combine access column with other columns
  const columns = [
    accessColumn,
    ...baseColumns,
    {
      header: "Handling",
      accessorKey: "besvarelse",
      enableSorting: false,
      enableHiding: false,
      sortingFn: (rowA: any, rowB: any) => {
        const getPriority = (row: any) =>
          row.original.documents?.length > 0 ? 2 : 1;
        return getPriority(rowB) - getPriority(rowA);
      },
      headerClassName: "sticky top-[62px] bg-white z-40 shadow-sm w-1/5",
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: { sorting, pagination, columnVisibility },
    onSortingChange: (updater) => {
      setSorting(updater);
      setSortKey((prev) => prev + 1); // Trigger staggered animation on sort
    },
    onColumnVisibilityChange: setColumnVisibility,
    manualSorting: false,
    enableSorting: true,
  });

  const handlePagination = useCallback(
    (action: "next" | "previous" | number) => {
      setPagination((prev) => ({
        pageSize: typeof action === "number" ? action : prev.pageSize,
        pageIndex:
          typeof action === "number"
            ? 0
            : action === "next"
              ? Math.min(prev.pageIndex + 1, table.getPageCount() - 1)
              : Math.max(0, prev.pageIndex - 1),
      }));
    },
    [table]
  );

  // Oppdater synlige kandidater når paginering eller data endres
  useEffect(() => {
    const visibleCandidates = table
      .getRowModel()
      .rows.map((row) => row.original.candidateNumber);
    updateVisibleCandidates(visibleCandidates);
    setCurrentPageCandidates(visibleCandidates); // Add this
  }, [pagination, table, updateVisibleCandidates, setCurrentPageCandidates]);

  // Tilbakestill paginering når resetPage er true
  useEffect(() => {
    if (resetPage) setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [resetPage]);

  // Tilbakestill paginering når datalengden endres (filter ble anvendt)
  useEffect(() => {
    if (data.length !== prevDataLength) {
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      setPrevDataLength(data.length);
    }
  }, [data.length, prevDataLength]);

  return (
    <div className="flex flex-col gap-3">
      <div className="text-sm text-muted-foreground flex justify-between items-center">
        <span>
          {data.length === getTotalCandidateCount()
            ? `Totalt: ${getTotalCandidateCount()} kandidater`
            : `Viser ${data.length} av ${getTotalCandidateCount()} kandidater`}
        </span>
        <div className="flex flex-col items-end gap-2">
          {/* <SignalRConnectionStatus /> */}
          <span>Tabellen oppdateres hvert 30. sekund</span>
        </div>
      </div>
      <motion.div
        className="border border-[#BAC6D8] rounded-md overflow-x-auto overflow-y-hidden"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <Table className="min-w-[800px] w-full table-fixed">
          <motion.thead
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2, delay: 0.1 }}
          >
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </motion.thead>
          <TableBody key={sortKey}>
            {table.getRowModel().rows.length === 0 ? (
              <motion.tr
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <TableCell
                  colSpan={columns.length + 2}
                  className="h-24 text-center"
                >
                  Ingen kandidater
                </TableCell>
              </motion.tr>
            ) : (
              table.getRowModel().rows.map((row, index) => (
                <motion.tr
                  key={`${sortKey}-${row.id}`}
                  tabIndex={0}
                  role="row"
                  data-state={row.getIsSelected() && "selected"}
                  className={`border-b border-[#BAC6D8] transition-colors cursor-pointer
                    ${candidatesWithLevertStatus[row.original.candidateNumber]
                      ? "bg-udirGreen-100 hover:bg-udirGreen-150"
                      : hasAbsence[row.original.candidateNumber]
                        ? "bg-udirGray-200 hover:!bg-udirGray-350"
                        : "hover:bg-blue-50"
                    }`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.05,
                    ease: "easeOut"
                  }}
                  onClick={(e) => e.stopPropagation()}
                >
                  {row.getVisibleCells().map((cell) => {
                    if (cell.column.id === "besvarelse") {
                      return (
                        <TableCell key={cell.id}>
                          <div className="flex flex-col gap-1 text-sm">
                            <ExamSubmissionActions
                              candidateInfo={row.original}
                            />
                          </div>
                        </TableCell>
                      );
                    }
                    return (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    );
                  })}
                </motion.tr>
              ))
            )}
          </TableBody>
        </Table>
      </motion.div>
      <div className="flex sm:items-center justify-between px-2 flex-col sm:flex-row">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Vis</span>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={(size) => handlePagination(Number(size))}
          >
            <SelectTrigger className="h-8 w-20">
              <SelectValue placeholder="50" />
            </SelectTrigger>
            <SelectContent>
              {[5, 50, 100].map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-sm text-muted-foreground">per side</span>
        </div>

        <div className="flex-1 text-sm text-muted-foreground sm:text-center">
          Side {pagination.pageIndex + 1} av {Math.max(1, table.getPageCount())}
        </div>

        <div className="flex items-center space-x-2">
          {["previous", "next"].map((action) => (
            <Button
              key={action}
              variant="outline"
              size="sm"
              onClick={() => handlePagination(action as "next" | "previous")}
              disabled={
                action === "previous"
                  ? pagination.pageIndex === 0
                  : pagination.pageIndex >= table.getPageCount() - 1
              }
            >
              {action === "previous" ? "Forrige" : "Neste"}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}
