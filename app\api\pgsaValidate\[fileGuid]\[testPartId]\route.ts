"use server";

import { getServerSession } from "next-auth/next";
import { getAccessToken } from "@/lib/getAccessToken";
import { authOptions } from "@/app/api/auth/authOptions";
import { ISession } from "@/interface/ISession";
import { NextResponse } from "next/server";
import { getAppInsightsServer } from "@/lib/appInsightsServer";
import { TestPartsEnum } from "@/enums/TestPart";

// Konfigurasjon
const config = {
  pgsaApiUrl: process.env.PGSA_PGSE_API_URL,
  clientId: process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "",
  clientSecret: process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "",
  scope: process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "",
  accesstokenKey: "PGSE:PGSA:AccessToken",
};

const telemetryClient = getAppInsightsServer();

// Hjelpefunksjon for validering
function validateInput(
  fileGuid: string,
  testPartId: TestPartsEnum
): string | null {
  if (!fileGuid || typeof fileGuid !== "string") {
    return "Ugyldig fileGuid";
  }
  if (!testPartId) {
    return "Ugyldig testPartId";
  }
  return null;
}

// Hjelpefunksjon for å hente access token
async function fetchAccessToken(): Promise<string | null> {
  try {
   
    return await getAccessToken(
      config.clientId,
      config.clientSecret,
      config.scope,
      config.accesstokenKey
    );
  } catch (error) {
    console.error("Feil ved henting av access token:", error);
    return null;
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ fileGuid: string; testPartId: TestPartsEnum }> }
) {
  const { fileGuid, testPartId } = await params;

  // Valider input
  const validationError = validateInput(fileGuid, testPartId);
  if (validationError) {
    return NextResponse.json({ error: validationError }, { status: 400 });
  }

  // Sjekk session
  const session: ISession | null = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Hent access token
  const accessToken = await fetchAccessToken();
  if (!accessToken) {
    return NextResponse.json(
      { error: "Failed to obtain access token" },
      { status: 500 }
    );
  }

  // Utfør API-kall
  try {
    const response = await fetch(
      `${config.pgsaApiUrl}/api/BatchProcessing/verify/${fileGuid}/${testPartId}`,
      {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      const errorData = await response.text();
      telemetryClient?.trackException({
        exception: new Error(`API-kall feilet: ${errorData}`),
        properties: {
          action: "updateFinishedNotification",
          fileGuid,
          testPartId,
          statusCode: response.status,
        },
      });
      return NextResponse.json(
        { error: errorData },
        { status: response.status }
      );
    }

    return NextResponse.json({ status: response.status });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "updateFinishedNotification",
        fileGuid,
        testPartId,
      },
    });
    console.error("Feil i updateFinishedNotification:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
