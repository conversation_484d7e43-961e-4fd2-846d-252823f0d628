"use server";

import { headers } from "next/headers";
import { NextRequest } from "next/server";

// Enkel regex for å sjekke om adressen er en gyldig IPv4 eller IPv6
const ipv4Pattern = /^(\d{1,3}\.){3}\d{1,3}$/;
const ipv6Pattern = /^([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4}$/;

export async function getClientIp(request?: NextRequest): Promise<string> {
  try {
    const header = await headers();
    const forwarded = header.get("x-forwarded-for") ?? "";
    const clientIp = getValidIpAddress(forwarded);

    return clientIp;
  } catch (error) {
    console.error("Error in getClientIp:", error);
    return ""; // Returner en tom streng som fallback
  }
}

function getValidIpAddress(forwarded: string): string {
  if (forwarded) {
    const addresses = forwarded.split(",");
    const firstAddress = addresses[0].trim();
    // Valider om den første adressen er en gyldig IP-adresse (IPv4 eller IPv6)
    if (isIpAddress(firstAddress)) {
      return firstAddress;
    }
  }
  return ""; // Returner en tom streng som fallback
}

function isIpAddress(address: string): boolean {
  return ipv4Pattern.test(address) || ipv6Pattern.test(address);
}
