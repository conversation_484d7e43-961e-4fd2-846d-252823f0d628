import { cookies } from "next/headers";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  (await cookies()).set("next-auth.session-token", "", {
    path: "/",
    maxAge: -1,
    secure: true,
    sameSite: "none",
  });
  (await cookies()).set("__Secure-next-auth.session-token", "", {
    path: "/",
    maxAge: -1,
    secure: true,
    sameSite: "none",
  });

  return new Response(JSON.stringify({ url: process.env.NEXTAUTH_URL }), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
