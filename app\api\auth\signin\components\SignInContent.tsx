"use client";

import React, { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn, useSession } from "next-auth/react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

export default function SignInContent() {
  const router = useRouter();
  const { status } = useSession();
  const searchParams = useSearchParams();

  useEffect(() => {
    async function signInWithProvider() {
      if (status === "unauthenticated") {
        await signIn("UIDP");
      } else if (status === "authenticated") {
        const state = searchParams.get("state");
        if (state) {
          router.push(decodeURIComponent(state));
        } else {
          router.push("/");
        }
      }
    }
    signInWithProvider();
  }, [status, router, searchParams]);

  return (
    <div
      style={{
        padding: "4rem",
      }}
    >
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          .spinner {
            animation: spin 1s linear infinite;
          }
        `}
      </style>
      <AiOutlineLoading3Quarters
        className="spinner"
        style={{ fontSize: "2rem" }}
      />
    </div>
  );
}
